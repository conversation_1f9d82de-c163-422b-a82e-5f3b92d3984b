### Implementation

Idempotency Key Implementation for Payment API

This module provides a idempotency mechanism with:
- Distributed locking using Redis
- Request body validation and hashing
- Error handling and logging
- Configurable timeouts and TTL

```python

from fastapi import FastAPI, Header, HTTPException, Response, status, Depends
from pydantic import BaseModel, Field, field_validator
from redis import Redis, ConnectionError as RedisConnectionError
import json
import hashlib
import time
import logging
import os
from typing import Optional, Dict, Any

from datetime import datetime, timezone
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration from environment variables
class Config:
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    IDEMPOTENCY_KEY_HEADER = os.getenv("IDEMPOTENCY_KEY_HEADER", "Idempotency-Key")
    IDEMPOTENCY_TTL_SECONDS = int(os.getenv("IDEMPOTENCY_TTL_SECONDS", "86400"))  # 24h
    IDEMPOTENCY_LOCK_TTL = int(os.getenv("IDEMPOTENCY_LOCK_TTL", "30"))  # 30s
    REQUEST_WAIT_TIMEOUT = int(os.getenv("REQUEST_WAIT_TIMEOUT", "5"))  # 5s
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))

config = Config()

# Redis connection with retry logic
def create_redis_connection() -> Redis:
    """Create Redis connection with error handling."""
    try:
        redis_client = Redis.from_url(
            config.REDIS_URL,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True
        )
        # Test connection
        redis_client.ping()
        logger.info("Redis connection established successfully")
        return redis_client
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        raise

redis = create_redis_connection()

# Pydantic models with validation
class PaymentRequest(BaseModel):
    amount: float = Field(..., gt=0, description="Payment amount (must be positive)")
    currency: str = Field(..., min_length=3, max_length=3, description="ISO currency code")
    card_token: str = Field(..., min_length=1, description="Tokenized card information")
    
    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        # Simulated currency validator: return as-is for now
        return v

    @field_validator('amount')
    @classmethod
    def validate_amount(cls, v):
        # Simulated amount validator: return as-is for now
        return v

class PaymentResponse(BaseModel):
    transaction_id: str
    status: str
    amount: float
    currency: str
    created_at: str
    
class IdempotencyService:
    """Service class for handling idempotency logic."""

    def __init__(self, redis_client: Redis):
        self.redis = redis_client

    def validate_idempotency_key(self, key: str) -> str:
        """Validate idempotency key according to banking industry best practices."""
        # Check for empty or None key
        if not key or len(key.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Idempotency key cannot be empty"
            )
        # Other validate...
        return key
        
    def _generate_cache_key(self, idempotency_key: str) -> str:
        """Generate Redis cache key for idempotency."""
        return f"idem:{idempotency_key}"
    
    def _generate_lock_key(self, idempotency_key: str) -> str:
        """Generate Redis lock key for idempotency."""
        return f"idem:{idempotency_key}:lock"
    
    def _hash_request(self, body: Dict[str, Any]) -> str:
        """Generate SHA-256 hash of request body for integrity validation."""
        try:
            # Ensure consistent serialization
            normalized_json = json.dumps(body, sort_keys=True, separators=(',', ':'))
            return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()
        except (TypeError, ValueError) as e:
            logger.error(f"Failed to hash request body: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request body format"
            )
    
    def get_cached_response(self, idempotency_key: str, body_hash: str) -> Optional[Dict[str, Any]]:
        """Retrieve cached response for idempotency key."""
        cache_key = self._generate_cache_key(idempotency_key)
        
        try:
            cached = self.redis.get(cache_key)
            if not cached:
                return None
                
            cached_obj = json.loads(cached)
            # Validate request body hasn't changed
            if cached_obj.get("body_hash") != body_hash:
                logger.warning(f"Idempotency key {idempotency_key} reused with different body")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="This idempotency key was used with a different request body",
                )
            
            logger.info(f"Cache hit for idempotency key: {idempotency_key}")
            return cached_obj
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode cached response for key {idempotency_key}: {e}")
            # Clear corrupted cache entry
            self.redis.delete(cache_key)
            return None
        except RedisConnectionError as e:
            logger.error(f"Redis connection error: {e}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cache service temporarily unavailable"
            )
    
    def acquire_lock(self, idempotency_key: str) -> bool:
        """Acquire distributed lock for idempotency key."""
        lock_key = self._generate_lock_key(idempotency_key)
        
        try:
            # Use SETNX with TTL for atomic lock acquisition
            return self.redis.set(
                lock_key,
                f"locked:{datetime.now(timezone.utc).isoformat()}",
                nx=True,
                ex=config.IDEMPOTENCY_LOCK_TTL
            )
        except RedisConnectionError as e:
            logger.error(f"Failed to acquire lock: {e}")
            return False
    
    def release_lock(self, idempotency_key: str) -> None:
        """Release distributed lock for idempotency key."""
        lock_key = self._generate_lock_key(idempotency_key)
        
        try:
            self.redis.delete(lock_key)
        except RedisConnectionError as e:
            logger.error(f"Failed to release lock: {e}")
    
    def cache_response(self, idempotency_key: str, body_hash: str,
                      response_data: str, http_status: int) -> None:
        """Cache response for idempotency key."""
        cache_key = self._generate_cache_key(idempotency_key)
        
        payload = {
            "body_hash": body_hash,
            "response_json": response_data,
            "http_status": http_status,
            "cached_at": datetime.now(timezone.utc).isoformat()
        }
        
        try:
            self.redis.set(
                cache_key,
                json.dumps(payload),
                ex=config.IDEMPOTENCY_TTL_SECONDS
            )
            logger.info(f"Cached response for idempotency key: {idempotency_key}")
        except RedisConnectionError as e:
            logger.error(f"Failed to cache response: {e}")

# Dependency injection
def get_idempotency_service() -> IdempotencyService:
    """Dependency injection for IdempotencyService."""
    return IdempotencyService(redis)

# FastAPI application
app = FastAPI(
    title="Payment API with Idempotency",
    description="Production-ready payment API with idempotency key support",
    version="1.0.0"
)

@app.post("/payments", response_model=PaymentResponse, status_code=201)
async def create_payment(
    payment_request: PaymentRequest,
    idempotency_service: IdempotencyService = Depends(get_idempotency_service),
    idem_key: str = Header(..., alias=config.IDEMPOTENCY_KEY_HEADER),
):
    """Create a new payment transaction with idempotency support."""
    # Validate idempotency key using service method
    idem_key = idempotency_service.validate_idempotency_key(idem_key)
    
    # Convert request to dict for hashing
    body = payment_request.model_dump()
    body_hash = idempotency_service._hash_request(body)
    
    # Check for cached response
    cached_response = idempotency_service.get_cached_response(idem_key, body_hash)
    if cached_response:
        return Response(
            content=cached_response["response_json"],
            status_code=cached_response["http_status"],
            media_type="application/json",
        )
    
    # Try to acquire lock
    if not idempotency_service.acquire_lock(idem_key):
        # Another request is processing - wait for result
        start_time = time.time()
        while time.time() - start_time < config.REQUEST_WAIT_TIMEOUT:
            time.sleep(0.05)  # 50ms polling interval
            
            cached_response = idempotency_service.get_cached_response(idem_key, body_hash)
            if cached_response:
                return Response(
                    content=cached_response["response_json"],
                    status_code=cached_response["http_status"],
                    media_type="application/json",
                )
        
        # Timeout - return conflict
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Request with this idempotency key is already being processed",
        )
    
    # Process payment (we own the lock)
    try:
        # Simulate payment processing
        # In production, this would call external payment gateway
        transaction_id = f"txn_{uuid.uuid4().hex[:12]}"
        
        response_obj = PaymentResponse(
            transaction_id=transaction_id,
            status="approved",
            amount=payment_request.amount,
            currency=payment_request.currency,
            created_at=datetime.now(timezone.utc).isoformat()
        )
        
        response_json = response_obj.model_dump_json()
        http_status = status.HTTP_201_CREATED
        
        # Cache the response
        idempotency_service.cache_response(idem_key, body_hash, response_json, http_status)
        
        logger.info(f"Payment processed successfully: {transaction_id}")
        
        return Response(
            content=response_json,
            status_code=http_status,
            media_type="application/json",
        )
        
    except Exception as e:
        logger.error(f"Payment processing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Payment processing failed"
        )
    finally:
        # Always release the lock
        idempotency_service.release_lock(idem_key)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

```